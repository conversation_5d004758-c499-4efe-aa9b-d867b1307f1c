import 'package:equatable/equatable.dart';
import 'sector_type.dart';
import 'organization.dart';

/// Additional data entity for Step 3 registration
class AdditionalData extends Equatable {
  final String firstName;
  final String lastName;
  final SectorType sectorType;
  final Organization? governmentAgency;
  final Organization? ministry;
  final Organization? department;
  final String departmentName;

  const AdditionalData({
    required this.firstName,
    required this.lastName,
    required this.sectorType,
    this.governmentAgency,
    this.ministry,
    this.department,
    required this.departmentName,
  });

  @override
  List<Object?> get props => [
    firstName,
    lastName,
    sectorType,
    governmentAgency,
    ministry,
    department,
    departmentName,
  ];

  AdditionalData copyWith({
    String? firstName,
    String? lastName,
    SectorType? sectorType,
    Organization? governmentAgency,
    Organization? ministry,
    Organization? department,
    String? departmentName,
  }) {
    return AdditionalData(
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      sectorType: sectorType ?? this.sectorType,
      governmentAgency: governmentAgency ?? this.governmentAgency,
      ministry: ministry ?? this.ministry,
      department: department ?? this.department,
      departmentName: departmentName ?? this.departmentName,
    );
  }

  /// Check if all required fields are filled
  bool get isValid {
    return firstName.isNotEmpty &&
           lastName.isNotEmpty &&
           departmentName.isNotEmpty &&
           (sectorType == SectorType.private || governmentAgency != null);
  }
}
