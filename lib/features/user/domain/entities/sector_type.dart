import 'package:equatable/equatable.dart';

/// Sector type entity for government/private sector selection
class SectorType extends Equatable {
  final int id;
  final String name;
  final String displayName;

  const SectorType({
    required this.id,
    required this.name,
    required this.displayName,
  });

  @override
  List<Object?> get props => [id, name, displayName];

  /// Static instances for common sector types
  static const government = SectorType(
    id: 1,
    name: 'government',
    displayName: 'ภาครัฐ',
  );

  static const private = SectorType(
    id: 2,
    name: 'private',
    displayName: 'ภาคเอกชน',
  );

  /// Get all available sector types
  static List<SectorType> get all => [government, private];
}
