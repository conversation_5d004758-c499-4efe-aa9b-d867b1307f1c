import 'package:get_it/get_it.dart';
import 'package:mcdc/features/user/data/datasources/auth_storage_data_source.dart';
import 'package:mcdc/features/user/data/datasources/user_api_data_source.dart';
import 'package:mcdc/features/poll/data/datasources/feedback_local_datasource.dart';
import 'package:mcdc/features/user/data/datasources/user_profile_local_datasource.dart';
import 'package:mcdc/features/otp/data/datasources/otp_api_data_source.dart';

/// Initialize all datasources used in the application
void initDataSources(GetIt sl) {
  // User Data Source
  sl.registerLazySingleton<UserApiDataSource>(
    () => UserApiDataSourceImpl(sl()),
  );
  sl.registerLazySingleton<AuthStorageDataSource>(
    () => AuthStorageDataSourceImpl(sl()),
  );

  // Feedback Data Sources
  sl.registerLazySingleton<FeedbackLocalDataSource>(
    () => FeedbackLocalDataSourceImpl(),
  );

  // User Profile Data Source
  sl.registerLazySingleton<UserProfileLocalDataSource>(
    () => UserProfileLocalDataSourceImpl(),
  );

  // OTP Data Source
  sl.registerLazySingleton<OtpApiDataSource>(() => OtpApiDataSourceImpl(sl()));
}
